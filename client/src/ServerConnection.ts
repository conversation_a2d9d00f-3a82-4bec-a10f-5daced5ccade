import WebSocket from 'ws';
import axios from 'axios';

export interface CapturedImage {
  path: string;
  preview: string;
  timestamp: string;
}

export interface WebcamDevice {
  id: string;
  name: string;
}

export class ServerConnection {
  private ws: WebSocket | null = null;
  private serverUrl: string;
  private wsUrl: string;
  private reconnectInterval: NodeJS.Timeout | null = null;
  private isConnecting: boolean = false;
  private eventHandlers: Map<string, Function[]> = new Map();

  constructor(serverUrl: string = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.wsUrl = serverUrl.replace('http', 'ws');
    this.connect();
  }

  private connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    console.log('Connecting to server:', this.wsUrl);

    try {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.on('open', () => {
        console.log('Connected to server');
        this.isConnecting = false;
        
        // Clear reconnect interval if it exists
        if (this.reconnectInterval) {
          clearInterval(this.reconnectInterval);
          this.reconnectInterval = null;
        }
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing message from server:', error);
        }
      });

      this.ws.on('close', () => {
        console.log('Disconnected from server');
        this.isConnecting = false;
        this.ws = null;
        this.scheduleReconnect();
      });

      this.ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
        this.ws = null;
        this.scheduleReconnect();
      });
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectInterval) {
      return; // Already scheduled
    }

    console.log('Scheduling reconnect in 5 seconds...');
    this.reconnectInterval = setInterval(() => {
      this.connect();
    }, 5000);
  }

  private handleMessage(message: any): void {
    console.log('Received message from server:', message);
    
    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data);
        } catch (error) {
          console.error('Error in event handler:', error);
        }
      });
    }
  }

  public on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  public off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  // API methods to communicate with server
  public async captureScreenshot(): Promise<CapturedImage> {
    try {
      const response = await axios.post(`${this.serverUrl}/capture/screenshot`);
      if (response.data.success) {
        return {
          path: response.data.path,
          preview: response.data.preview,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error(response.data.error || 'Failed to capture screenshot');
      }
    } catch (error) {
      console.error('Error capturing screenshot:', error);
      throw error;
    }
  }

  public async captureWebcam(): Promise<CapturedImage> {
    try {
      const response = await axios.post(`${this.serverUrl}/capture/webcam`);
      if (response.data.success) {
        return {
          path: response.data.path,
          preview: response.data.preview,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error(response.data.error || 'Failed to capture webcam image');
      }
    } catch (error) {
      console.error('Error capturing webcam image:', error);
      throw error;
    }
  }

  public async getWebcamDevices(): Promise<WebcamDevice[]> {
    try {
      const response = await axios.get(`${this.serverUrl}/webcam/devices`);
      if (response.data.success) {
        return response.data.devices;
      } else {
        throw new Error(response.data.error || 'Failed to get webcam devices');
      }
    } catch (error) {
      console.error('Error getting webcam devices:', error);
      throw error;
    }
  }

  public async setWebcamDevice(deviceId: string): Promise<boolean> {
    try {
      const response = await axios.post(`${this.serverUrl}/webcam/device`, { deviceId });
      return response.data.success;
    } catch (error) {
      console.error('Error setting webcam device:', error);
      throw error;
    }
  }

  public async checkServerHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.serverUrl}/health`, { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      console.error('Server health check failed:', error);
      return false;
    }
  }

  public disconnect(): void {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
