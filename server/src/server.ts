import express from 'express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import { ScreenshotHelper } from './helpers/ScreenshotHelper';
import { WebcamHelper } from './helpers/WebcamHelper';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors({
  origin: true, // Allow all origins for network access
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Initialize helpers
let screenshotHelper: ScreenshotHelper;
let webcamHelper: WebcamHelper;

// Store connected clients
const clients = new Set<any>();

// WebSocket connection handling
wss.on('connection', (ws) => {
  console.log('Client connected');
  clients.add(ws);

  ws.on('close', () => {
    console.log('Client disconnected');
    clients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
});

// Broadcast to all connected clients
function broadcast(message: any) {
  const messageStr = JSON.stringify(message);
  clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      client.send(messageStr);
    }
  });
}

// API Routes
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.post('/capture/screenshot', async (req, res) => {
  try {
    console.log('Screenshot capture requested');
    
    if (!screenshotHelper) {
      return res.status(500).json({ error: 'Screenshot helper not initialized' });
    }

    const screenshotPath = await screenshotHelper.takeScreenshot(
      () => {}, // No window to hide on server
      () => {}  // No window to show on server
    );

    const preview = await screenshotHelper.getImagePreview(screenshotPath);
    
    // Broadcast to all connected clients
    broadcast({
      type: 'screenshot-captured',
      data: {
        path: screenshotPath,
        preview: preview,
        timestamp: new Date().toISOString()
      }
    });

    res.json({ 
      success: true, 
      path: screenshotPath, 
      preview: preview 
    });
  } catch (error) {
    console.error('Error capturing screenshot:', error);
    res.status(500).json({
      error: 'Failed to capture screenshot',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

app.post('/capture/webcam', async (req, res) => {
  try {
    console.log('Webcam capture requested');
    
    if (!webcamHelper) {
      return res.status(500).json({ error: 'Webcam helper not initialized' });
    }

    const imagePath = await webcamHelper.captureImage();
    const preview = await webcamHelper.getImagePreview(imagePath);
    
    // Broadcast to all connected clients
    broadcast({
      type: 'webcam-captured',
      data: {
        path: imagePath,
        preview: preview,
        timestamp: new Date().toISOString()
      }
    });

    res.json({ 
      success: true, 
      path: imagePath, 
      preview: preview 
    });
  } catch (error) {
    console.error('Error capturing webcam image:', error);
    res.status(500).json({
      error: 'Failed to capture webcam image',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

app.get('/webcam/devices', async (req, res) => {
  try {
    if (!webcamHelper) {
      return res.status(500).json({ error: 'Webcam helper not initialized' });
    }

    const devices = await webcamHelper.getAvailableWebcams();
    res.json({ success: true, devices });
  } catch (error) {
    console.error('Error getting webcam devices:', error);
    res.status(500).json({
      error: 'Failed to get webcam devices',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

app.post('/webcam/device', async (req, res) => {
  try {
    const { deviceId } = req.body;
    
    if (!webcamHelper) {
      return res.status(500).json({ error: 'Webcam helper not initialized' });
    }

    const success = await webcamHelper.setWebcamDevice(deviceId);
    res.json({ success });
  } catch (error) {
    console.error('Error setting webcam device:', error);
    res.status(500).json({
      error: 'Failed to set webcam device',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

// Initialize helpers and start server
async function initializeServer() {
  try {
    console.log('Initializing server helpers...');
    
    // Initialize screenshot helper
    screenshotHelper = new ScreenshotHelper('queue'); // Default view
    
    // Initialize webcam helper
    webcamHelper = new WebcamHelper();
    
    console.log('Server helpers initialized successfully');
  } catch (error) {
    console.error('Failed to initialize server helpers:', error);
  }
}

const PORT = parseInt(process.env.PORT || '3001', 10);
const HOST = process.env.HOST || '0.0.0.0'; // Listen on all interfaces for network access

server.listen(PORT, HOST, async () => {
  console.log(`Server running on ${HOST}:${PORT}`);
  console.log(`HTTP API available at: http://${HOST}:${PORT}`);
  console.log(`WebSocket server available at: ws://${HOST}:${PORT}`);
  console.log(`WebSocket server ready for connections`);
  await initializeServer();
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
