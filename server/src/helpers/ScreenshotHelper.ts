// Server ScreenshotHelper.ts - Adapted from electron version
import fs from "node:fs"
import path from "node:path"
import os from "node:os"
import { v4 as uuidv4 } from "uuid"
import screenshot from "screenshot-desktop"
import { execFile } from "node:child_process"
import { promisify } from "node:util"

const execFileAsync = promisify(execFile)

export class ScreenshotHelper {
  private view: string
  private screenshotQueue: string[] = []
  private extraScreenshotQueue: string[] = []
  private tempDir: string
  private screenshotsDir: string
  private extraScreenshotsDir: string

  constructor(view: string) {
    this.view = view
    
    // Setup directories - use a server-specific location
    const appDataDir = process.env.NODE_ENV === 'production'
      ? path.join(os.homedir(), '.ic-share-server')
      : path.join(process.cwd(), 'server-data')
    
    this.tempDir = path.join(appDataDir, 'temp')
    this.screenshotsDir = path.join(appDataDir, 'screenshots')
    this.extraScreenshotsDir = path.join(appDataDir, 'extra_screenshots')

    // Create directories if they don't exist
    this.ensureDirectoriesExist()
    
    console.log("ScreenshotHelper initialized with directories:", {
      temp: this.tempDir,
      screenshots: this.screenshotsDir,
      extraScreenshots: this.extraScreenshotsDir
    })
  }

  private ensureDirectoriesExist(): void {
    try {
      if (!fs.existsSync(this.tempDir)) {
        fs.mkdirSync(this.tempDir, { recursive: true })
      }
      if (!fs.existsSync(this.screenshotsDir)) {
        fs.mkdirSync(this.screenshotsDir, { recursive: true })
      }
      if (!fs.existsSync(this.extraScreenshotsDir)) {
        fs.mkdirSync(this.extraScreenshotsDir, { recursive: true })
      }
    } catch (error) {
      console.error("Error creating directories:", error)
    }
  }

  public getScreenshotQueue(): string[] {
    return [...this.screenshotQueue]
  }

  public getExtraScreenshotQueue(): string[] {
    return [...this.extraScreenshotQueue]
  }

  public clearQueues(): void {
    this.screenshotQueue = []
    this.extraScreenshotQueue = []
  }

  public deleteScreenshot(filepath: string): boolean {
    try {
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath)
      }

      // Remove from queues
      this.screenshotQueue = this.screenshotQueue.filter(path => path !== filepath)
      this.extraScreenshotQueue = this.extraScreenshotQueue.filter(path => path !== filepath)
      
      return true
    } catch (error) {
      console.error("Error deleting screenshot:", error)
      return false
    }
  }

  public async getImagePreview(filepath: string): Promise<string> {
    try {
      if (!fs.existsSync(filepath)) {
        throw new Error(`File does not exist: ${filepath}`)
      }

      const imageBuffer = fs.readFileSync(filepath)
      const base64 = imageBuffer.toString('base64')
      return `data:image/png;base64,${base64}`
    } catch (error) {
      console.error("Error getting image preview:", error)
      throw error
    }
  }

  private async captureScreenshot(): Promise<Buffer> {
    try {
      console.log("Starting screenshot capture...");
      
      // For Windows, try multiple methods
      if (process.platform === 'win32') {
        return await this.captureWindowsScreenshot();
      } 
      
      // For macOS and Linux, use buffer directly
      console.log("Taking screenshot on non-Windows platform");
      const buffer = await screenshot({ format: 'png' });
      console.log(`Screenshot captured successfully, size: ${buffer.length} bytes`);
      return buffer;
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      throw new Error(`Failed to capture screenshot: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async captureWindowsScreenshot(): Promise<Buffer> {
    console.log("Attempting Windows screenshot capture with multiple methods");
    
    // Method 1: Try screenshot-desktop with filename first
    try {
      const tempFile = path.join(this.tempDir, `temp-${uuidv4()}.png`);
      console.log(`Taking Windows screenshot to temp file (Method 1): ${tempFile}`);
      
      await screenshot({ filename: tempFile });
      
      if (fs.existsSync(tempFile)) {
        const buffer = await fs.promises.readFile(tempFile);
        console.log(`Method 1 successful, screenshot size: ${buffer.length} bytes`);
        
        // Cleanup temp file
        try {
          await fs.promises.unlink(tempFile);
        } catch (cleanupErr) {
          console.warn("Failed to clean up temp file:", cleanupErr);
        }
        
        return buffer;
      } else {
        console.log("Method 1 failed: File not created");
        throw new Error("Screenshot file not created");
      }
    } catch (error) {
      console.warn("Windows screenshot Method 1 failed:", error);
    }

    // Method 2: Try PowerShell approach
    try {
      console.log("Trying Windows screenshot Method 2: PowerShell");
      const tempFile = path.join(this.tempDir, `ps-temp-${uuidv4()}.png`);
      
      const psScript = `
      Add-Type -AssemblyName System.Windows.Forms
      Add-Type -AssemblyName System.Drawing
      $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
      $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
      $graphics = [System.Drawing.Graphics]::FromImage($bmp)
      $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size)
      $bmp.Save('${tempFile.replace(/\\/g, '\\\\')}', [System.Drawing.Imaging.ImageFormat]::Png)
      $graphics.Dispose()
      $bmp.Dispose()
      `;
      
      // Execute PowerShell
      await execFileAsync('powershell', [
        '-NoProfile', 
        '-ExecutionPolicy', 'Bypass',
        '-Command', psScript
      ]);
      
      // Check if file exists and read it
      if (fs.existsSync(tempFile)) {
        const buffer = await fs.promises.readFile(tempFile);
        console.log(`Method 2 successful, screenshot size: ${buffer.length} bytes`);
        
        // Cleanup
        try {
          await fs.promises.unlink(tempFile);
        } catch (err) {
          console.warn("Failed to clean up PowerShell temp file:", err);
        }
        
        return buffer;
      } else {
        throw new Error("PowerShell screenshot file not created");
      }
    } catch (error) {
      console.warn("Windows screenshot Method 2 failed:", error);
    }

    // Method 3: Fallback to direct buffer (may not work on all Windows versions)
    try {
      console.log("Trying Windows screenshot Method 3: Direct buffer");
      const buffer = await screenshot({ format: 'png' });
      console.log(`Method 3 successful, screenshot size: ${buffer.length} bytes`);
      return buffer;
    } catch (error) {
      console.error("All Windows screenshot methods failed:", error);
      throw new Error("Failed to capture screenshot on Windows");
    }
  }

  public async takeScreenshot(
    hideMainWindow?: () => void,
    showMainWindow?: () => void
  ): Promise<string> {
    console.log("Taking screenshot in view:", this.view)
    
    // No window hiding needed on server
    if (hideMainWindow) hideMainWindow()
    
    // Small delay to ensure any UI changes are complete
    await new Promise((resolve) => setTimeout(resolve, 100))

    let screenshotPath = ""
    try {
      // Get screenshot buffer using cross-platform method
      const screenshotBuffer = await this.captureScreenshot();
      
      if (!screenshotBuffer || screenshotBuffer.length === 0) {
        throw new Error("Screenshot capture returned empty buffer");
      }

      // Generate filename and save
      const filename = `screenshot-${Date.now()}-${uuidv4()}.png`
      
      if (this.view === "queue") {
        screenshotPath = path.join(this.screenshotsDir, filename)
        this.screenshotQueue.push(screenshotPath)
      } else {
        screenshotPath = path.join(this.extraScreenshotsDir, filename)
        this.extraScreenshotQueue.push(screenshotPath)
      }

      // Write the buffer to file
      await fs.promises.writeFile(screenshotPath, screenshotBuffer)
      console.log(`Screenshot saved successfully: ${screenshotPath}`)
      
      return screenshotPath
    } catch (error) {
      console.error("Error in takeScreenshot:", error)
      throw error
    } finally {
      // No window showing needed on server
      if (showMainWindow) showMainWindow()
    }
  }
}
